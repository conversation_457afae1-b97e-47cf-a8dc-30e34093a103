import { contentHeader, noAuth<PERSON>eader, noContent<PERSON>eader } from "@/utils/header";
import { apiSlice } from "../apiSlice";

// TypeScript interfaces for Projects
export interface Project {
  projectId: string;
  name: string;
  description?: string;
  initials?: string;
  link?: string;
  priority?: string;
  tier?: string;
  visibiliy?: string;
  bank?: string;
  account_no?: string;
  website_link?: string;
}

export interface ProjectsApiResponse {
  count: number;
  next?: string;
  previous?: string;
  results: Project[];
}

export interface ProjectsQueryParams {
  name?: string;
  visibiliy?: string;
  search?: string;
  ordering?: string;
  page?: number;
  page_size?: number;
}

export const projectsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getCustomer: builder.query({
      query: (params) => ({
        url: "/customers/all-customers",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["Projects"],
    }),

    getDashboardStats: builder.query({
      query: (params) => ({
        url: "/inventory/dashboard-stats",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["Projects"],
    }),

    getProjects: builder.query<ProjectsApiResponse, ProjectsQueryParams>({
      query: (params) => ({
        url: "/inventory/projects",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["Projects"],
    }),

    getProjectDetails: builder.query({
      query: (id) => ({
        url: `/inventory/projects/${id}`,
        method: "GET",
      }),
      providesTags: ["Projects"],
    }),

    updateProject: builder.mutation({
      query: (data) => ({
        url: `/inventory/projects/${data?.projectId}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: ["Projects"],
    }),

    getPlots: builder.query({
      query: (params) => ({
        url: "/inventory/plots",
        method: "GET",
        params: params,
        headers: contentHeader(),
      }),
      providesTags: ["Projects"],
    }),

    getPlotBookings: builder.query({
      query: (params) => ({
        url: "/inventory/plot-booking",
        method: "GET",
        params: params,
        headers: contentHeader(),
      }),
      providesTags: ["Projects"],
    }),

    bookPlot: builder.mutation({
      query: (data) => ({
        url: "/inventory/plot-booking",
        method: "POST",
        body: data,
        headers: noContentHeader(),
      }),
      invalidatesTags: ["Projects"],
    }),

    updatePlotBooking: builder.mutation({
      query: ({ id, formData }) => ({
        url: `/inventory/plot-booking/${id}`,
        method: "PATCH",
        body: formData,
        // headers: noContentHeader(),
      }),
      invalidatesTags: ["Projects"],
    }),

    addReserveDiasporaPlot: builder.mutation({
      query: (data) => ({
        url: "/inventory/diaspora-reservation",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Projects"],
    }),

    getInventoryLogs: builder.query({
      query: (params) => ({
        url: "/inventory/logs",
        method: "GET",
        params: params,
      }),
      providesTags: ["Projects"],
    }),

    getReserveDiasporaPlot: builder.query({
      query: (params) => ({
        url: "/inventory/diaspora-reservation",
        method: "GET",
        params: params,
      }),
      providesTags: ["Projects"],
    }),

    editReserveDiasporaPlot: builder.mutation({
      query: (data) => ({
        url: "/inventory/diaspora-reservation/" + data?.id,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: ["Projects"],
    }),

    getForex: builder.query({
      query: (params) => ({
        url: "/inventory/forex",
        method: "GET",
        params: params,
      }),
      providesTags: ["Projects"],
      transformResponse: (response) => {
        return {
          results: response.data.results || [],
        };
      },
    }),

    updateForex: builder.mutation({
      query: (data) => ({
        url: "/inventory/forex/" + data?.id,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: ["Projects"],
    }),

    getMpesaTransactions: builder.query({
      query: (params) => ({
        url: "/inventory/mpesa-transactions",
        method: "GET",
        params: params,
      }),
      providesTags: ["Projects"],
    }),

    editMpesaTransactions: builder.mutation({
      query: (data) => ({
        url: "/inventory/mpesa-transactions/" + data?.trans_id,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: ["Projects"],
    }),
  }),
});

export const {
  useGetDashboardStatsQuery,
  useGetPlotsQuery,
  useLazyGetPlotsQuery,
  useLazyGetProjectsQuery,
  useGetProjectsQuery,
  useGetProjectDetailsQuery,
  useUpdateProjectMutation,
  useBookPlotMutation,
  useUpdatePlotBookingMutation,
  useGetCustomerQuery,
  useLazyGetCustomerQuery,
  useGetPlotBookingsQuery,
  useLazyGetPlotBookingsQuery,
  useAddReserveDiasporaPlotMutation,
  useGetReserveDiasporaPlotQuery,
  useEditReserveDiasporaPlotMutation,
  useGetInventoryLogsQuery,
  useGetMpesaTransactionsQuery,
  useEditMpesaTransactionsMutation,
  useGetForexQuery,
  useUpdateForexMutation,
} = projectsApiSlice;
